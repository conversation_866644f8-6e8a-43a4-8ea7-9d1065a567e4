# ContentVideoO95Fragment 播放按钮消失问题修复说明

## 问题描述

在 ContentVideoO95Fragment 组件中存在一个UI显示问题：当视频播放结束并且画质增强处理完成后，播放按钮消失不见了，用户无法重新播放视频。

## 问题根因分析

通过代码分析，发现问题的根本原因有以下几点：

### 1. MotionLayout 配置不完整
- 在 `scene_vp_video_o95.xml` 中，播放按钮 `ivPlayToggle222` 只在 `vp_video_portrait`（竖屏）状态下有配置
- 在 `vp_video_landscope_fold` 和 `vp_video_landscope_unfold`（横屏）状态下缺少播放按钮的约束配置
- 导致在某些状态切换时，播放按钮失去了布局约束而消失

### 2. 画质增强完成后的状态管理不完善
- `updateVideoSourceOnly` 方法虽然设置了播放按钮的可见性，但没有确保 MotionLayout 的状态正确
- 缺少对横竖屏状态的正确处理和 MotionLayout 状态的强制刷新

### 3. 横竖屏切换逻辑存在缺陷
- `checkRotationConfiguration` 方法在横屏时会将播放按钮的 alpha 设置为 0
- 但由于 MotionLayout 约束集配置不完整，导致按钮在状态切换时完全消失

## 修复方案

### 1. 完善 MotionLayout 场景配置

**文件**: `feature_miwearglasses/src/main/res/xml/scene_vp_video_o95.xml`

为所有 ConstraintSet 添加播放按钮的约束配置：

- **竖屏状态** (`vp_video_portrait`): 播放按钮完全可见，alpha=1
- **横屏折叠状态** (`vp_video_landscope_fold`): 播放按钮存在但透明，alpha=0
- **横屏展开状态** (`vp_video_landscope_unfold`): 播放按钮存在但透明，alpha=0

### 2. 优化画质增强完成后的处理逻辑

**文件**: `ContentVideoO95Fragment.kt`

#### 修改 `updateVideoSourceOnly` 方法：
- 确保播放按钮的可见性设置为 `View.VISIBLE`
- 根据横竖屏状态正确设置按钮的交互性
- 强制刷新 MotionLayout 状态，确保约束正确应用
- 添加状态检查，确保 MotionLayout 处于正确的状态

#### 修改 `updateVideoSourceAndPause` 方法：
- 同样确保播放按钮的可见性和交互性
- 正确处理暂停状态下的画质增强完成场景
- 添加 MotionLayout 状态管理逻辑

## 修复后的行为

### 竖屏模式
- 视频播放结束且画质增强完成后，播放按钮完全可见且可点击
- 用户可以正常点击播放按钮重新播放视频

### 横屏模式
- 视频播放结束且画质增强完成后，播放按钮存在但透明（符合横屏UI设计）
- 按钮不可点击（横屏模式下通过其他方式控制播放）
- 但按钮的约束和状态得到正确维护

### 暂停状态
- 在暂停状态下画质增强完成后，播放按钮保持可见
- 用户可以继续正常的播放/暂停操作

## 测试验证

创建了 `ContentVideoO95FragmentTest.kt` 测试文件，包含以下测试用例：

1. **竖屏模式下播放完成且画质增强完成后播放按钮可见性测试**
2. **横屏模式下播放完成且画质增强完成后播放按钮状态测试**
3. **暂停状态下画质增强完成后播放按钮可见性测试**
4. **MotionLayout约束集配置完整性验证**

## 关键修改点总结

1. **MotionLayout 配置完善**: 为所有状态添加播放按钮约束
2. **状态管理优化**: 确保画质增强完成后正确设置按钮状态
3. **横竖屏适配**: 正确处理不同屏幕方向下的按钮显示逻辑
4. **强制状态刷新**: 使用 `post` 方法确保 MotionLayout 状态正确应用

## 注意事项

- 修改涉及 MotionLayout 的场景配置，需要确保所有相关的 UI 状态都得到正确处理
- 在测试时需要验证横竖屏切换、视频播放状态变化、画质增强流程等多种场景
- 建议在不同设备和屏幕尺寸上进行充分测试

## 相关文件

- `feature_miwearglasses/src/main/res/xml/scene_vp_video_o95.xml`
- `feature_miwearglasses/src/main/java/com/superhexa/supervision/feature/miwearglasses/presentation/space/detail/ContentVideoO95Fragment.kt`
- `feature_miwearglasses/src/test/java/com/superhexa/supervision/feature/miwearglasses/presentation/space/detail/ContentVideoO95FragmentTest.kt`
