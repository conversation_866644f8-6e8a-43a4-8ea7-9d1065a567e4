package com.superhexa.supervision.feature.miwearglasses.presentation.space.detail

import android.view.View
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.lifecycle.Lifecycle
import androidx.test.core.app.ActivityScenario
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.databinding.ItemViewpagerMediaVideoDetailO95Binding
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.videoplayer.EmptyControlVideoView
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * ContentVideoO95Fragment 播放按钮可见性测试
 * 主要测试视频播放结束且画质增强完成后，播放按钮是否仍然可见
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
@Config(sdk = [28])
class ContentVideoO95FragmentTest {

    private lateinit var fragment: ContentVideoO95Fragment
    private lateinit var mockBinding: ItemViewpagerMediaVideoDetailO95Binding
    private lateinit var mockVideoView: EmptyControlVideoView
    private lateinit var mockMotionLayout: MotionLayout
    private lateinit var mockPlayButton: View

    @Before
    fun setUp() {
        // 初始化 mock 对象
        mockBinding = mockk(relaxed = true)
        mockVideoView = mockk(relaxed = true)
        mockMotionLayout = mockk(relaxed = true)
        mockPlayButton = mockk(relaxed = true)

        // 设置 mock 行为
        every { mockBinding.baseVideoView } returns mockVideoView
        every { mockBinding.WrapperRoot } returns mockMotionLayout
        every { mockBinding.ivPlayToggle222 } returns mockPlayButton
        every { mockVideoView.currentState } returns EmptyControlVideoView.CURRENT_STATE_AUTO_COMPLETE
        every { mockMotionLayout.currentState } returns R.id.vp_video_portrait
        every { mockPlayButton.visibility } returns View.VISIBLE
        every { mockPlayButton.isClickable } returns true

        // 创建 fragment 实例
        fragment = ContentVideoO95Fragment()
    }

    @After
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `测试视频播放完成且画质增强完成后播放按钮仍然可见_竖屏模式`() {
        // Given: 视频播放完成状态
        every { mockVideoView.currentState } returns EmptyControlVideoView.CURRENT_STATE_AUTO_COMPLETE
        every { mockMotionLayout.currentState } returns R.id.vp_video_portrait
        
        // 模拟画质增强完成的 MediaBean
        val mediaBean = MediaBean().apply {
            processState = MediaBean.Process_Complete
            downloadState = MediaBean.Complete
            path = "/test/enhanced_video.mp4"
            identifier = "test_video_id"
        }

        // When: 调用 updateVideoSourceOnly 方法（模拟画质增强完成后的处理）
        // 由于这是私有方法，我们通过反射调用
        val method = ContentVideoO95Fragment::class.java.getDeclaredMethod(
            "updateVideoSourceOnly", 
            String::class.java
        )
        method.isAccessible = true
        
        // 使用 spyk 来部分 mock fragment
        val spyFragment = spyk(fragment, recordPrivateCalls = true)
        every { spyFragment.isLandScope() } returns false
        
        // Then: 验证播放按钮的可见性和交互性
        verify(exactly = 1) { mockPlayButton.visibility = View.VISIBLE }
        verify(exactly = 1) { mockPlayButton.isClickable = true }
        verify(exactly = 1) { mockMotionLayout.transitionToState(R.id.vp_video_portrait) }
    }

    @Test
    fun `测试视频播放完成且画质增强完成后播放按钮状态_横屏模式`() {
        // Given: 视频播放完成状态，横屏模式
        every { mockVideoView.currentState } returns EmptyControlVideoView.CURRENT_STATE_AUTO_COMPLETE
        every { mockMotionLayout.currentState } returns R.id.vp_video_landscope_fold
        
        // 模拟画质增强完成的 MediaBean
        val mediaBean = MediaBean().apply {
            processState = MediaBean.Process_Complete
            downloadState = MediaBean.Complete
            path = "/test/enhanced_video.mp4"
            identifier = "test_video_id"
        }

        // When: 调用 updateVideoSourceOnly 方法（横屏模式）
        val method = ContentVideoO95Fragment::class.java.getDeclaredMethod(
            "updateVideoSourceOnly", 
            String::class.java
        )
        method.isAccessible = true
        
        // 使用 spyk 来部分 mock fragment
        val spyFragment = spyk(fragment, recordPrivateCalls = true)
        every { spyFragment.isLandScope() } returns true
        
        // Then: 验证播放按钮在横屏模式下的状态
        // 横屏模式下按钮应该存在但不可点击（由 MotionLayout 控制透明度）
        verify(exactly = 1) { mockPlayButton.visibility = View.VISIBLE }
        verify(exactly = 1) { mockPlayButton.isClickable = false }
        verify(exactly = 1) { mockMotionLayout.transitionToState(R.id.vp_video_landscope_fold) }
    }

    @Test
    fun `测试暂停状态下画质增强完成后播放按钮可见性`() {
        // Given: 视频暂停状态
        every { mockVideoView.currentState } returns EmptyControlVideoView.CURRENT_STATE_PAUSE
        every { mockMotionLayout.currentState } returns R.id.vp_video_portrait
        
        // When: 调用 updateVideoSourceAndPause 方法
        val method = ContentVideoO95Fragment::class.java.getDeclaredMethod(
            "updateVideoSourceAndPause", 
            String::class.java,
            Long::class.java
        )
        method.isAccessible = true
        
        // 使用 spyk 来部分 mock fragment
        val spyFragment = spyk(fragment, recordPrivateCalls = true)
        every { spyFragment.isLandScope() } returns false
        
        // Then: 验证播放按钮在暂停状态下的可见性
        verify(exactly = 1) { mockPlayButton.visibility = View.VISIBLE }
        verify(exactly = 1) { mockPlayButton.isClickable = true }
        verify(exactly = 1) { mockMotionLayout.transitionToState(R.id.vp_video_portrait) }
    }

    @Test
    fun `测试MotionLayout约束集包含播放按钮配置`() {
        // 这个测试验证 MotionLayout 的场景配置文件是否正确包含了播放按钮的约束
        // 由于这是布局配置测试，主要验证配置文件的正确性
        
        // Given: MotionLayout 的不同状态
        val portraitState = R.id.vp_video_portrait
        val landscapeFoldState = R.id.vp_video_landscope_fold
        val landscapeUnfoldState = R.id.vp_video_landscope_unfold
        
        // When & Then: 验证所有状态都包含播放按钮的约束配置
        // 这个测试主要是文档化的作用，确保开发者知道所有状态都应该包含播放按钮配置
        assert(portraitState != 0) { "竖屏状态应该包含播放按钮约束" }
        assert(landscapeFoldState != 0) { "横屏折叠状态应该包含播放按钮约束" }
        assert(landscapeUnfoldState != 0) { "横屏展开状态应该包含播放按钮约束" }
    }
}
