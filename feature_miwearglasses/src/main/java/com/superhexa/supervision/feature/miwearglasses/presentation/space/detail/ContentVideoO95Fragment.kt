@file:Suppress(
    "<PERSON><PERSON><PERSON><PERSON>",
    "TooGenericExceptionCaught",
    "NestedBlockDepth",
    "TooManyFunctions",
    "MagicNumber"
)

package com.superhexa.supervision.feature.miwearglasses.presentation.space.detail

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.player.IjkPlayerManager
import com.shuyu.gsyvideoplayer.player.PlayerFactory
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_AUTO_COMPLETE
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_ERROR
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_PAUSE
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_PLAYING
import com.shuyu.gsyvideoplayer.video.base.GSYVideoView.CURRENT_STATE_PLAYING_BUFFERING_START
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.databinding.ItemViewpagerMediaVideoDetailO95Binding
import com.superhexa.supervision.feature.miwearglasses.presentation.space.detail.MediaDetailO95FragmentViewModel.Companion.Pause
import com.superhexa.supervision.feature.miwearglasses.presentation.space.detail.MediaDetailO95FragmentViewModel.Companion.Play
import com.superhexa.supervision.feature.miwearglasses.presentation.space.detail.MediaDetailO95FragmentViewModel.Companion.SetData
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95DownloadProgressEvent
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95NetDisconnectEvent
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95ProcessEvent
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.customviews.videoseekbar.OnVideoSeekChange
import com.superhexa.supervision.library.base.customviews.zoom.ZoomLayout
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.presentation.viewmodel.getViewModel
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.db.bean.MediaBean.Process_Complete
import com.superhexa.supervision.library.db.bean.MediaBean.Process_Error
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.videoplayer.EmptyControlVideoView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber
import java.io.File

/**
 * 类描述:文件控件详情页面的ViewPager中的视频详情
 * 创建日期:2021/7/26 on 10:23 下午
 * 作者: FengPeng
 */
@ExperimentalCoroutinesApi
class ContentVideoO95Fragment : InjectionFragment(R.layout.item_viewpager_media_video_detail_o95) {

    private val buglyTag = ContentVideoO95Fragment::class.java.simpleName
    private val viewBinding: ItemViewpagerMediaVideoDetailO95Binding by viewBinding()
    private val viewModel by lazy {
        getViewModel(requireActivity(), MediaDetailO95FragmentViewModel::class.java)
    }

    // 将静态变量移动到这里作为成员变量
    private var currentVideoPath: String = ""
    private var progressPosition: Long = 0L

    override fun needDefaultbackground() = false

    private val videoView: EmptyControlVideoView by lazy {
        viewBinding.baseVideoView.isNeedShowWifiTip = false
        viewBinding.baseVideoView
    }
    private var position: Int = InvalidIndex
    private var videoPath = ""
    private var pendingSeekPositionMs: Long = -1L // -1 表示没有待处理的 seek 请求
    private var bean: MediaBean? = null

    // 记录开始滑动是， 播放器状态，滑动结束后恢复其状态
    private var seekBarUpdateJob: Job? = null // 更新播放器进度条的任务
    private var nowPlayerStatus = -1
    private var lastState = -1
    private var isInOnResume = false
    private var enterTime = 0L
    private var isUserPaused = false // 记录是否为用户手动暂停
    private var lastSeekTime = 0L // 记录上次seekTo的时间，用于节流控制

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)

        enterTime = System.currentTimeMillis()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        checkRotationConfiguration()
        if (view != null && isAdded) {
            arguments?.let { bundle ->
                position = bundle.getInt(BundleKey.Position, InvalidIndex)
                if (position != InvalidIndex) {
                    viewModel.mutableList.value?.let {
                        if (it.size > position) {
                            bean = it[position]
                            bean?.let { bean ->
                                Timber.d("bean %s", bean)
                                configViewByBean(bean, false)
                            }
                        }
                    }
                }
            }
            // 监听ViewPager滑进的页面
            viewModel.currentPageIndex.observe(viewLifecycleOwner) { index ->
                if (position == InvalidIndex) return@observe
                if (index == position) {
                    Timber.d("当前页面${position}被选中，准备播放 $videoPath")
                    updateSeekBar(0)
                    loadVideoThumbnailWithCache()
                    GSYVideoManager.instance().releaseMediaPlayer()
                    viewLifecycleOwner.lifecycleScope.launch {
                        delay(600)
                        if (viewModel.currentPageIndex.value == position) {
                            resetAndPlay(videoPath)
                        }
                    }
                }
            }
            // 监听ViewPager滑出的页面
            viewModel.previousPageIndex.observe(viewLifecycleOwner) { index ->
                if (position == InvalidIndex) return@observe
                if (index == position) {
                    Timber.d("当前页面${position}被滑出，释放资源")
                    loadVideoThumbnailWithCache()
                }
            }
            subscribeUI()
            initListener()
            PlayerFactory.setPlayManager(IjkPlayerManager::class.java)
        }
    }

    @Suppress("ComplexMethod")
    private fun subscribeUI() {
        videoView.setGSYVideoProgressListener { progress, secProgress, currentPosition, duration ->
            // 保持播放器状态与进度条任务状态同步
            if (videoView.currentState == CURRENT_STATE_PLAYING) {
                if (seekBarUpdateJob?.isActive != true) {
                    updateSeekBarProgress(false)
                }
            }
        }
        videoView.setStateUiListener { state ->
            // 检查Fragment的生命周期
            if (!isAdded || view == null) {
                return@setStateUiListener
            }
            logPlayerState(state)
            // 是否可以使用${videoView.currentState}？
            if (lastState != state) {
                lastState = state
            }
            // 播放器需要处于暂停状态
            when (state) {
                CURRENT_STATE_PLAYING_BUFFERING_START -> return@setStateUiListener
                // 播放器处于播放状态
                CURRENT_STATE_PLAYING -> {
                    isUserPaused = false
                    // 确定进度条任务已启动
                    updateSeekBarProgress(false)
                    // 视频准备就绪时延迟隐藏缩略图
                    viewLifecycleOwner.lifecycleScope.launch {
                        delay(300L)
                        viewBinding.preview.llPreview.visibleOrgone(false)
                    }
                    // 处理延迟的 seekTo 请求
                    handlePendingSeekRequest()
                }

                CURRENT_STATE_PAUSE -> {
                    cancelSeekBarUpdateJob()
                    viewBinding.ivPlayToggle222.isSelected = false
                }

                CURRENT_STATE_ERROR -> cancelSeekBarUpdateJob()
                CURRENT_STATE_AUTO_COMPLETE -> {
                    // 自动播放结束强制矫正滚动条进度
                    updateSeekBar(videoView.duration.toLong())
                    progressPosition = 0L
                    // 更新按钮状态
                    viewModel.notifyPlayOrPauseLiveDate.value = false
                    cancelSeekBarUpdateJob()
                }
            }
        }

        viewBinding.ivPlayToggle222.clickDebounce(viewLifecycleOwner, intervalTime) {
            if (videoView.isVisible) {
                isSelected = !isSelected

                val isPlayingOrPause = viewModel.notifyPlayOrPauseLiveDate.value
                if (isPlayingOrPause != null && isPlayingOrPause) {
                    viewModel.notifyPlayOrPauseLiveDate.value = false
                    isUserPaused = true
                    pauseVideo()
                } else {
                    isUserPaused = false
                    Timber.i("clickDebounce playVideo")
                    viewModel.notifyPlayOrPauseLiveDate.value = true
                    playVideo()
                }
            }
        }

        viewBinding.vsbar.setOnSeekChange(object : OnVideoSeekChange {
            override fun onSeekStart() {
                if (!isAdded || view == null) {
                    return
                }
                Timber.e("updateSeekBarProgress : onSeekStart  ")
                Timber.d(
                    "onSeekStart  videoView.currentPosition %s  videoView.duration %s bean?.path %s",
                    videoView.currentPosition,
                    videoView.duration,
                    bean?.path
                )
                nowPlayerStatus = videoView.currentState
                if (nowPlayerStatus == CURRENT_STATE_PLAYING) {
                    Timber.i("onSeekStart: pauseVideo")
                    pauseVideo() // 用户滑动时暂停播放
                }
            }

            override fun onSeekChange(scale: Float) {
                if (scale in vaildRange) {
                    progressPosition = (videoView.duration * scale / hundred).toLong()
                    // 节流控制：每500ms最多更新一次视频画面
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastSeekTime >= SEEK_CHANGE_INTERVAL) {
                        if (videoView.currentState == CURRENT_STATE_PLAYING) {
                            pauseVideo()
                        }
                        if (videoView.currentState == CURRENT_STATE_PAUSE) {
                            videoView.seekTo(progressPosition)
                        }
                        lastSeekTime = currentTime
                        Timber.d("onSeekChange : $scale seekTo $progressPosition (节流更新)")
                    }

                    // 时间显示始终实时更新
                    val nowTime = DateTimeUtils.videoDuration(progressPosition)
                    viewBinding.tvProgress.text = nowTime
                    Timber.d("onSeekChange : $scale seekTo $progressPosition")
                }
            }

            override fun onSeekEnd() {
                if (!isAdded || view == null) {
                    return
                }
                viewLifecycleOwner.lifecycleScope.launch {
                    videoView.seekTo(progressPosition)
                    delay(300L) // 稍微延迟，等待播放器状态稳定
                    // 拖拽结束时确保视频跳转到最终位置
                    Timber.i("onSeekEnd: currentState ${videoView.currentState}")
                    logPlayerState(videoView.currentState)
                    if (videoView.currentState == CURRENT_STATE_PAUSE) {
                        Timber.i("onSeekEnd: 最终seekTo $progressPosition")
                        // 检查是否拖拽到视频末尾附近（允许少量误差）
                        val isNearEnd = progressPosition > (videoView.duration - 1000L)
                        val playOrPause = viewModel.notifyPlayOrPauseLiveDate.value
                        Timber.i("onSeekEnd : playOrPause=$playOrPause isNearEnd=$isNearEnd")
                        if (playOrPause == true || isNearEnd) {
                            // 确保进度条更新任务重新启动（如果播放器状态监听没有触发的话）
                            videoView.resumeStart()
                            if (seekBarUpdateJob?.isActive != true) {
                                updateSeekBarProgress(false)
                                viewModel.notifyPlayOrPauseLiveDate.value = true
                            }
                        }
                    }
                }
            }
        })

        viewModel.videoProgressLiveData.observe(viewLifecycleOwner) { progress ->
            viewBinding.vsbar.currentScale = progress
        }

        viewModel.barFold.observe(viewLifecycleOwner) { unfold ->
            if (isLandScope()) {
                viewBinding.WrapperRoot.transitionToState(
                    if (unfold) {
                        R.id.vp_video_landscope_unfold
                    } else {
                        R.id.vp_video_landscope_fold
                    }
                )
            }
        }
    }

    private fun initListener() {
        viewModel.notifyPlayOrPauseLiveDate.observe(viewLifecycleOwner) {
            viewBinding.ivPlayToggle222.isSelected = it
        }
        viewBinding.baseVideoViewWrapper.setZoomLayoutGestureListener(object :
                ZoomLayout.ZoomLayoutGestureListener {
                override fun onScrollBegin() {
                    Timber.d("onScrollBegin")
                }

                override fun onScaleGestureBegin() {
                    Timber.d("onScaleGestureBegin")
                }

                override fun onDoubleTap() {
                    Timber.d("onDoubleTap")
                }

                override fun onSingleTap() {
                    if (isLandScope()) {
                        viewModel.barFold.value = !viewModel.barFold.value!!
                    }
                }
            })
    }

    private fun getVideoPath(bean: MediaBean): String {
        return when (bean.processState) {
            Process_Complete -> {
                if (fileIsExists(bean.path)) bean.path else bean.vidoTempPath
            }

            else -> {
                if (fileIsExists(bean.vidoTempPath)) bean.vidoTempPath else bean.path
            }
        }
    }

    private fun fileIsExists(path: String?): Boolean {
        return try {
            val file = path?.let { File(it) }
            file?.exists() == true
        } catch (e: Exception) {
            false
        }
    }

    private fun configViewByBean(bean: MediaBean, fromOnEvent: Boolean) {
        when {
            bean.downloadState == MediaBean.Complete && bean.path != null -> {
                viewBinding.include.llError.visibleOrgone(false)
                videoPath = getVideoPath(bean)

                // 确保预览图可见，并加载本地视频的缩略图
                loadVideoThumbnailWithCache()
                viewBinding.preview.progressBar.visibleOrgone(false)
                if (!fromOnEvent) {
                    viewModel.playLiveData.value = VideoState(SetData, videoPath)
                }
                // 视频处理完毕后，无需重新加载进度条预览图
                if (!fromOnEvent || bean.processState != Process_Complete) {
                    val heightPx = AppUtils.dp2px(requireContext(), seekBarHeight)
                    viewBinding.vsbar.setVideoPath(videoPath, heightPx)
                    Timber.d("进度条加载成功 vsbar.setVideoPath()")
                }
                // 根据处理状态显示对应的UI
                updateUiForProcessState(bean, fromOnEvent)
            }

            bean.downloadState == MediaBean.Downloading ||
                bean.downloadState == MediaBean.Wait -> {
                Timber.e(
                    "configViewByBean : ${bean.downloadState} -> ${bean.fileName} ${bean.path} "
                )
                viewBinding.include.llError.visibleOrgone(false)
                viewBinding.preview.llPreview.visibleOrgone(true)
                GlideUtils.loadUrl(
                    requireContext(),
                    bean.thumbnailUrl,
                    viewBinding.preview.ivPreview,
                    isVideo = true
                )
                viewBinding.preview.progressBar.setProgress(bean.downloadProgress)
            }

            bean.downloadState == MediaBean.Error -> {
                Timber.e(
                    "configViewByBean : ${bean.downloadState} -> ${bean.fileName} ${bean.path} "
                )
                viewBinding.preview.llPreview.visibleOrgone(false)
                viewBinding.include.llError.visibleOrgone(true)
            }

            else -> {
            }
        }
    }

    @SuppressLint("SourceLockedOrientationActivity")
    @Suppress("EmptyFunctionBlock")
    override fun rotateConfig() {
    }

    fun playVideo() {
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
            val isReplayRequest = lastState == CURRENT_STATE_AUTO_COMPLETE
            val isMute = viewModel.muteToggleState.value?.isOpen ?: false
            Timber.d("play called isReplayRequest:$lastState, isMute:$isMute")
            videoView.setMute(isMute)
            if (isReplayRequest || lastState == -1) {
                // 点击重新播放视频
                Timber.d("isReplayRequest:$lastState $progressPosition")
                if (progressPosition == 0L || progressPosition > videoView.duration - 100) {
                    loadVideoThumbnailWithCache()
                    resetAndPlay(videoPath)
                } else {
                    videoView.resumeStart()
                    updateSeekBarProgress(false)
                }
            } else {
                videoView.start()
                // 确保恢复播放时 ViewModel 状态也正确
                viewModel.playLiveData.value = VideoState(Play, videoPath)
                viewModel.notifyPlayOrPauseLiveDate.value = true
            }
        }
    }

    fun pauseVideo() {
        videoView.pause()
        Timber.d("pause called")
    }

    override fun onPause() {
        Timber.e("currentPosition onPause videoPath : $videoPath")
        isInOnResume = false
        // 只在视频正在播放时自动暂停
        if (bean?.downloadState == MediaBean.Complete && videoView.isPlaying) {
            // 判断是否是因为ViewPager滑动导致的onPause
            val isCurrentPage = viewModel.currentPageIndex.value == null ||
                viewModel.currentPageIndex.value == position
            if (isCurrentPage) {
                pauseVideo()
                Timber.i("当前页面$position，onPause（应用进入后台）")
            } else {
                Timber.i("当前页面$position，onPause（ViewPager滑动）")
            }
        }
        super.onPause()
    }

    override fun onResume() {
        super.onResume()
        isInOnResume = true
        checkRotationConfiguration()
        Timber.d("muteToggleState.value  onResume ${currentVideoPath == videoPath}")
        // 首次进入页面或切换新视频，自动播放
        logPlayerState(videoView.currentState)
        // fix:第一个视频不自动播放的问题
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
            if (viewModel.currentPageIndex.value == null) {
                resetAndPlay(videoPath)
            }
            if (!isUserPaused && videoView.currentState == CURRENT_STATE_PAUSE) {
                playVideo()
            }
        }
    }

    override fun onStop() {
        super.onStop()
        O95Statistic.cpaEvent(
            "Photo_Video_Manage_Time",
            tip = "1676.0.0.0.43045",
            hasDurationSec = true,
            durationSec = enterTime
        )
    }

    private fun updateSeekBarProgress(isTouched: Boolean, scale: Float = -1f) {
        if (view != null && isAdded) {
            if (isTouched) {
                if (scale in vaildRange) {
                    val cur = (videoView.duration * scale / hundred).toLong()
                    if (videoView.isPlaying) {
                        pauseVideo()
                    }
                    videoView.seekTo(cur)
                    updateSeekBar(cur)
                }
            } else {
                // 启动新任务前先取消原任务
                cancelSeekBarUpdateJob()
                seekBarUpdateJob = viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
                    delay(500)
                    Timber.i("启动进度条更新任务")
                    currentVideoPath = videoPath
                    while (isInOnResume) {
                        // Timber.d("updateSeekBarProgress isPlaying ${videoView.isPlaying}")
                        if (!videoView.isPlaying) {
                            delay(updateInteval)
                            continue
                        }
                        /**
                         *  当在MediaDetailFragment.onViewCreated 中使用ijkplayer 或者系统播放器时
                         *  当currentPosition 返回的不正确的时候，不进行更新操作，由于某些暂不知名的原因
                         当你滑动控制栏seekTo 进度后，currentPosition 返回的数据会出现短暂的不正确的现象
                         表现为 突然为0 ，或者滞后几秒钟，
                         */
                        val currentPosition = videoView.currentPosition
                        val position =
                            if (currentPosition > progressPosition || currentPosition == 0L) {
                                currentPosition
                            } else {
                                progressPosition
                            }
                        updateSeekBar(position)
                        delay(updateInteval)
                    }
                }
            }
        }
    }

    private fun updateSeekBar(position: Long = videoView.currentPosition) {
        if (isAdded && view != null) {
            viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                val adjustHundred = adjustHundred(videoView.duration, 180)
                val progress = position * adjustHundred / videoView.duration
                viewBinding.vsbar.currentScale = progress

                progressPosition = position
                val nowTime = DateTimeUtils.videoDuration(position)
                viewBinding.tvProgress.text = nowTime
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Timber.d("newConfig %s orientation %s", newConfig, resources.configuration.orientation)
        checkRotationConfiguration()
    }

    private fun checkRotationConfiguration() {
        if (isLandScope()) {
            // 横屏模式：让MotionLayout控制播放按钮的透明度，不直接设置alpha
            viewBinding.ivPlayToggle222.isClickable = false
            viewBinding.WrapperRoot.transitionToState(R.id.vp_video_landscope_fold)
            // 确保播放按钮的visibility保持VISIBLE，让MotionLayout的alpha约束生效
            if (viewBinding.ivPlayToggle222.visibility != View.VISIBLE) {
                viewBinding.ivPlayToggle222.visibility = View.VISIBLE
            }
        } else {
            Timber.e("isLandScope:no")
            // 竖屏模式：确保播放按钮完全可见和可交互
            viewBinding.ivPlayToggle222.alpha = 1f
            viewBinding.ivPlayToggle222.isClickable = true
            viewBinding.ivPlayToggle222.visibility = View.VISIBLE
            viewBinding.WrapperRoot.transitionToState(R.id.vp_video_portrait)
        }

        Timber.d("checkRotationConfiguration: 屏幕方向=${if (isLandScope()) "横屏" else "竖屏"}, 播放按钮visibility=${viewBinding.ivPlayToggle222.visibility}, alpha=${viewBinding.ivPlayToggle222.alpha}")
    }

    override fun onDestroyView() {
        EventBus.getDefault().unregister(this)
        if (bean?.downloadState == MediaBean.Complete) {
            Timber.e("VPVideoContentFragment viewModel.playLiveData videoFragment")
        }
        isUserPaused = false
        super.onDestroyView()
    }

    fun toSetMute(isMute: Boolean) {
        Timber.e("toSetMute:$isMute")
        videoView.setMute(isMute)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: O95NetDisconnectEvent) {
        if (!isAdded || view == null) {
            return
        }
        bean?.let {
            if (it.identifier == event.bean.identifier) {
                configViewByBean(event.bean, true)
            } else {
                // 重置下载状态不是完成的为异常，以便显示撕裂图片
                if (it.downloadState != MediaBean.Complete) {
                    it.downloadState = MediaBean.Error
                    configViewByBean(it, true)
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: O95DownloadProgressEvent) {
        if (!isAdded || view == null) {
            return
        }
        bean?.let {
            if (it.identifier == event.identifier) {
                it.path = event.path
                it.vidoTempPath = event.tempVideoPath
                it.contentUri = event.contentUri
                it.downloadState = event.downloadState
                it.downloadProgress = event.downloadProgress
                // 使用configViewByBean更新UI状态
                configViewByBean(it, true)
                // 如果下载完成，设置为画质增强状态
                if (it.downloadState == MediaBean.Complete) {
                    it.processState = MediaBean.Process_Processing
                    viewModel.avaiableControllbar.postValue(false)
                    // 下载完成后，使用tempVideoPath播放视频
                    if (isInOnResume) {
                        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                            event.tempVideoPath?.let { tempPath -> resetAndPlay(tempPath) }
                        }
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: O95ProcessEvent) {
        if (!isAdded || view == null) {
            return
        }
        bean?.let {
            if (it.identifier == event.identifier) {
                it.processState = event.processState
                it.path = event.path
                Timber.i("O95ProcessEvent $event")
                configViewByBean(it, true)
                // 处理完成后需要重新加载视频(因为路径视频路径会变化)
                if (isInOnResume && event.path.isNotEmpty()) {
                    viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                        when (event.processState) {
                            Process_Complete -> {
                                when (videoView.currentState) {
                                    CURRENT_STATE_AUTO_COMPLETE -> {
                                        // 播放已完成状态，不需要继续播放，只需更新UI
                                        Timber.i("O95ProcessEvent 视频处理完成，但播放已结束：${event.path}")
                                        updateVideoSourceOnly(event.path)
                                    }
                                    CURRENT_STATE_PAUSE -> {
                                        // 暂停状态，更新视频源但保持暂停
                                        Timber.i("O95ProcessEvent 视频处理完成，保持暂停状态：${event.path}")
                                        updateVideoSourceAndPause(event.path, progressPosition)
                                    }
                                    else -> {
                                        // 播放状态或其他状态，正常继续播放
                                        Timber.i("O95ProcessEvent 视频处理完成，继续播放：${event.path}")
                                        playVideoFrom(event.path, progressPosition)
                                    }
                                }
                            }

                            Process_Error -> {
                                Timber.i("O95ProcessEvent 视频处理错误，重新播放：${event.path}")
                                resetAndPlay(event.path)
                            }
                        }
                    }
                }
            }
        }
    }

    companion object {
        fun newInstance(position: Int): ContentVideoO95Fragment {
            val args = Bundle()
            args.putInt(BundleKey.Position, position)
            val fragment = ContentVideoO95Fragment()
            fragment.arguments = args
            return fragment
        }

        const val InvalidIndex = -1
        const val updateInteval = 10L
        const val hundred = 100f
        val vaildRange = 0f..100.1f
        const val intervalTime = 300L
        const val seekBarHeight = 40f
        const val SEEK_CHANGE_INTERVAL = 200L // 进度条拖拽时的节流间隔（毫秒）
    }

    /**
     * 按偏移量重新矫正百分比
     * @param duration 视频总时长（毫秒）
     */
    private fun adjustHundred(duration: Int, offset: Int): Float {
        if (duration <= offset) return hundred
        // 计算校正因子，考虑offset毫秒的偏移量
        return duration * 100f / (duration - offset).toFloat()
    }

    /**
     * 重置播放器状态然后播放视频
     */
    private suspend fun resetAndPlay(path: String) {
        if (bean?.downloadState == MediaBean.Complete) {
            // 1. 重置播放状态
            currentVideoPath = ""
            progressPosition = 0L
            videoView.stop()
            videoView.release()
            videoView.releaseAllVideo()
            updateSeekBar(0)
            Timber.i("播放状态重置成功")
            delay(200)
            // 2. 设置播放器
            videoView.setUp(path, true, "")
            val muteState = viewModel.muteToggleState.value
            val isMute = muteState?.isOpen ?: false
            videoView.setMute(isMute)
            Timber.i("播放器初始化成功：$path")
            delay(200)
            // 3. 开始播放
            videoView.start()
            Timber.i("resetAndPlay 开始播放：$path")

            // 4. 更新UI状态
            viewModel.notifyPlayOrPauseLiveDate.value = true
            viewModel.playLiveData.value = VideoState(Play, path)
            viewBinding.ivPlayToggle222.isSelected = true
            if (muteState != null) {
                viewModel.muteToggleState.value = muteState.copy(state = 1)
            }
            currentVideoPath = path
            Timber.i("播放器UI状态更新完成")
        }
    }

    /**
     * 加载并显示本页视频(videoPath)的预览图
     */
    private fun loadVideoThumbnailWithCache() {
        GlideUtils.loadVideoThumbnailWithCache(
            requireContext(),
            videoPath,
            viewBinding.preview.ivPreview
        )
        viewBinding.preview.llPreview.visibleOrgone(true)
    }

    /**
     * 从指定路径和位置开始播放视频
     * @param targetPath 视频文件路径
     * @param positionMs 开始播放的位置（毫秒）
     */
    private fun playVideoFrom(targetPath: String, positionMs: Long) {
        Timber.d("playVideoFrom: Path: $targetPath, Position: $positionMs ms")
        // 重置播放视频的状态
        this.videoPath = targetPath
        currentVideoPath = targetPath // 确保 onResume 逻辑正确
        progressPosition = positionMs
        // 清理播放器资源
        videoView.stop()
        videoView.release()
        videoView.setUp(targetPath, true, "")
        // 设置静音状态
        val isMute = viewModel.muteToggleState.value?.isOpen ?: false
        videoView.setMute(isMute)
        try {
            // 标记期望的跳转位置
            if (positionMs > 0) {
                this.pendingSeekPositionMs = positionMs
            } else {
                this.pendingSeekPositionMs = -1L // 从头播放或无需跳转
                updateSeekBar(0L) // 如果从头播放，立即更新 seekbar
            }
            // 开始播放视频
            videoView.start()
            viewModel.notifyPlayOrPauseLiveDate.value = true
            viewModel.playLiveData.value = VideoState(Play, targetPath)
            viewBinding.ivPlayToggle222.isSelected = true
            // viewBinding.preview.llPreview.visibleOrgone(false)
        } catch (e: Exception) {
            Timber.e(e.printDetail(), "Error for path: $targetPath")
        }
    }

    /**
     * 处理待处理的跳转请求
     */
    private fun handlePendingSeekRequest() {
        // 处理延迟的 seekTo 请求
        if (pendingSeekPositionMs != -1L) {
            val duration = videoView.duration
            val seekToMs = pendingSeekPositionMs
            pendingSeekPositionMs = -1L // 清除标记，避免重复 seek

            if (duration > 0 && seekToMs > 0 && seekToMs < duration) {
                videoView.seekTo(seekToMs)
                updateSeekBar(seekToMs) // 显式更新UI到目标位置
            } else {
                // 如果 seek 位置无效或为0，则让其自然播放，或依赖 updateSeekBarProgress 更新
                if (seekToMs == 0L) updateSeekBar(0L)
            }
        }
    }

    private fun updateUiForProcessState(bean: MediaBean, fromOnEvent: Boolean) {
        when (bean.processState) {
            MediaBean.Process_Processing -> {
                // 处理中显示"画质增强中"
                viewBinding.tvQualityStatusText.text = getString(R.string.video_quality_enhancing)
                viewBinding.pbQualityStatus.visibleOrgone(true)
                viewBinding.llQualityStatusContainer.visibleOrgone(true)
            }

            MediaBean.Process_Error -> {
                // 处理失败显示"画质增强失败"
                if (fromOnEvent) {
                    viewBinding.preview.llPreview.visibleOrgone(false)
                    viewBinding.tvQualityStatusText.text =
                        getString(R.string.video_quality_enhance_failed)
                    viewBinding.pbQualityStatus.visibleOrgone(false)
                    viewBinding.llQualityStatusContainer.visibleOrgone(true)
                    viewLifecycleOwner.lifecycleScope.launch {
                        delay(2000L) // 错误提示显示2秒
                        viewBinding.llQualityStatusContainer.visibleOrgone(false)
                    }
                }
            }

            else -> {
                // 如果是处理完成状态（来自Event事件）
                if (fromOnEvent && bean.processState == Process_Complete) {
                    viewBinding.tvQualityStatusText.text =
                        getString(R.string.video_quality_enhance_completed)
                    viewBinding.llQualityStatusContainer.visibleOrgone(true)
                    viewBinding.pbQualityStatus.visibleOrgone(false)
                    viewBinding.ivQualityStatus.visibleOrgone(true)
                    viewLifecycleOwner.lifecycleScope.launch {
                        delay(2000L) // 延迟2秒隐藏
                        viewBinding.llQualityStatusContainer.visibleOrgone(false)
                    }
                }
            }
        }
    }

    /**
     * 取消进度条更新任务
     */
    private fun cancelSeekBarUpdateJob() {
        if (seekBarUpdateJob?.isActive == true) {
            Timber.d("取消进度条更新任务")
            seekBarUpdateJob?.cancel()
            seekBarUpdateJob = null
        }
    }

    /**
     * 仅更新视频源，不改变播放状态
     * 用于播放已完成状态下的画质增强完成处理
     * @param targetPath 新的视频文件路径
     */
    private fun updateVideoSourceOnly(targetPath: String) {
        Timber.d("updateVideoSourceOnly: Path: $targetPath")
        // 更新视频路径
        this.videoPath = targetPath
        currentVideoPath = targetPath
        // 重新设置播放器源，但不改变播放状态
        videoView.setUp(targetPath, true, "")
        // 设置静音状态
        val isMute = viewModel.muteToggleState.value?.isOpen ?: false
        videoView.setMute(isMute)
        // 更新UI状态
        viewModel.playLiveData.value = VideoState(Play, targetPath)
        viewModel.notifyPlayOrPauseLiveDate.value = false // 确保播放按钮显示为暂停状态
        viewBinding.ivPlayToggle222.isSelected = false // 播放完成状态显示为暂停图标

        // 调试：记录调用前的状态
        debugPlayButtonState("updateVideoSourceOnly-调用前")

        // 强制确保播放按钮可见性，使用多重保障
        ensurePlayButtonVisibility()

        // 调试：记录调用后的状态
        viewBinding.WrapperRoot.postDelayed({
            debugPlayButtonState("updateVideoSourceOnly-调用后")
        }, 200)

        Timber.d("updateVideoSourceOnly completed: 播放按钮状态已更新")
    }

    /**
     * 确保播放按钮的可见性，使用多重保障机制
     * 这个方法专门处理画质增强完成后播放按钮的显示问题
     */
    private fun ensurePlayButtonVisibility() {
        Timber.d("ensurePlayButtonVisibility: 开始确保播放按钮可见性")

        // 使用调试辅助类进行诊断
        PlayButtonDebugHelper.checkPlayButtonState(
            viewBinding.ivPlayToggle222,
            viewBinding.WrapperRoot,
            isLandScope(),
            "ensurePlayButtonVisibility-开始"
        )

        // 第一重保障：直接设置可见性
        viewBinding.ivPlayToggle222.visibility = View.VISIBLE

        // 第二重保障：延迟设置，确保在所有其他操作完成后执行
        viewBinding.WrapperRoot.post {
            // 应用修复
            PlayButtonDebugHelper.applyFixes(
                viewBinding.ivPlayToggle222,
                viewBinding.WrapperRoot,
                isLandScope()
            )

            // 根据屏幕方向设置正确的MotionLayout状态
            if (isLandScope()) {
                // 确保MotionLayout处于正确的横屏状态
                val currentState = viewBinding.WrapperRoot.currentState
                if (currentState != R.id.vp_video_landscope_fold && currentState != R.id.vp_video_landscope_unfold) {
                    viewBinding.WrapperRoot.transitionToState(R.id.vp_video_landscope_fold)
                }
                Timber.d("ensurePlayButtonVisibility: 横屏模式，按钮透明但存在")
            } else {
                // 确保MotionLayout处于正确的竖屏状态
                if (viewBinding.WrapperRoot.currentState != R.id.vp_video_portrait) {
                    viewBinding.WrapperRoot.transitionToState(R.id.vp_video_portrait)
                }
                Timber.d("ensurePlayButtonVisibility: 竖屏模式，按钮完全可见")
            }

            // 第三重保障：再次延迟确认，防止被其他代码覆盖
            viewBinding.WrapperRoot.postDelayed({
                if (isAdded && view != null) {
                    // 最终检查和修复
                    PlayButtonDebugHelper.checkPlayButtonState(
                        viewBinding.ivPlayToggle222,
                        viewBinding.WrapperRoot,
                        isLandScope(),
                        "ensurePlayButtonVisibility-最终检查"
                    )
                }
            }, 150) // 延迟150ms确保所有状态切换完成
        }
    }

    /**
     * 更新视频源并保持暂停状态
     * 用于暂停状态下的画质增强完成处理
     * @param targetPath 新的视频文件路径
     * @param positionMs 暂停时的位置（毫秒）
     */
    private fun updateVideoSourceAndPause(targetPath: String, positionMs: Long) {
        Timber.d("updateVideoSourceAndPause: Path: $targetPath, Position: $positionMs ms")
        // 更新视频路径和位置
        this.videoPath = targetPath
        currentVideoPath = targetPath
        progressPosition = positionMs
        // 重新设置播放器源
        videoView.setUp(targetPath, true, "")
        // 设置静音状态
        val isMute = viewModel.muteToggleState.value?.isOpen ?: false
        videoView.setMute(isMute)
        try {
            // 跳转到之前的位置并暂停
            if (positionMs > 0) {
                videoView.seekTo(positionMs)
                updateSeekBar(positionMs)
            }
            videoView.pause() // 确保保持暂停状态
            // 更新UI状态为暂停
            viewModel.playLiveData.value = VideoState(Pause, targetPath)
            viewModel.notifyPlayOrPauseLiveDate.value = false
            viewBinding.ivPlayToggle222.isSelected = false

            // 使用相同的播放按钮可见性保障机制
            ensurePlayButtonVisibility()

            Timber.d("updateVideoSourceAndPause completed: 播放按钮状态已更新")
        } catch (e: Exception) {
            Timber.e(e.printDetail(), "Error updating video source and pause for path: $targetPath")
        }
    }

    /**
     * 调试方法：打印播放按钮的详细状态信息
     * 用于诊断播放按钮不可见的问题
     */
    private fun debugPlayButtonState(methodName: String) {
        if (isAdded && view != null) {
            val button = viewBinding.ivPlayToggle222
            val motionLayout = viewBinding.WrapperRoot

            Timber.d("=== 播放按钮状态调试 [$methodName] ===")
            Timber.d("按钮visibility: ${button.visibility} (${getVisibilityString(button.visibility)})")
            Timber.d("按钮alpha: ${button.alpha}")
            Timber.d("按钮isClickable: ${button.isClickable}")
            Timber.d("按钮isSelected: ${button.isSelected}")
            Timber.d("按钮width: ${button.width}, height: ${button.height}")
            Timber.d("按钮x: ${button.x}, y: ${button.y}")
            Timber.d("MotionLayout currentState: ${motionLayout.currentState}")
            Timber.d("MotionLayout progress: ${motionLayout.progress}")
            Timber.d("屏幕方向: ${if (isLandScope()) "横屏" else "竖屏"}")
            Timber.d("视频播放器状态: ${videoView.currentState}")
            Timber.d("=== 播放按钮状态调试结束 ===")
        }
    }

    private fun getVisibilityString(visibility: Int): String {
        return when (visibility) {
            View.VISIBLE -> "VISIBLE"
            View.INVISIBLE -> "INVISIBLE"
            View.GONE -> "GONE"
            else -> "UNKNOWN($visibility)"
        }
    }

    /**
     * 手动测试播放按钮修复效果的方法
     * 您可以在适当的地方调用这个方法来测试修复是否生效
     */
    fun testPlayButtonFix() {
        if (isAdded && view != null) {
            Timber.d("=== 手动测试播放按钮修复 ===")

            // 模拟画质增强完成的场景
            val testVideoPath = videoPath
            Timber.d("测试路径: $testVideoPath")

            // 调用修复方法
            ensurePlayButtonVisibility()

            // 延迟检查结果
            viewBinding.WrapperRoot.postDelayed({
                val button = viewBinding.ivPlayToggle222
                val isVisible = button.visibility == View.VISIBLE
                val alpha = button.alpha
                val isClickable = button.isClickable

                Timber.d("测试结果:")
                Timber.d("  播放按钮可见: $isVisible")
                Timber.d("  播放按钮透明度: $alpha")
                Timber.d("  播放按钮可点击: $isClickable")
                Timber.d("  屏幕方向: ${if (isLandScope()) "横屏" else "竖屏"}")

                val success = isVisible && (
                    (isLandScope() && alpha >= 0.2f) || // 横屏模式下应该有一定透明度
                    (!isLandScope() && alpha >= 0.9f)   // 竖屏模式下应该基本不透明
                )

                if (success) {
                    Timber.i("✅ 播放按钮修复测试通过！")
                } else {
                    Timber.e("❌ 播放按钮修复测试失败，需要进一步调试")
                }

                Timber.d("=== 测试结束 ===")
            }, 200)
        }
    }

    /**
     * 打印播放器状态日志
     * @param state 播放器状态码
     */
    private fun logPlayerState(state: Int) {
        val stateDesc = when (state) {
            0 -> getString(R.string.player_state_normal)
            1 -> getString(R.string.player_state_preparing)
            2 -> getString(R.string.player_state_playing)
            3 -> getString(R.string.player_state_buffering)
            5 -> getString(R.string.player_state_pause)
            6 -> getString(R.string.player_state_auto_complete)
            7 -> getString(R.string.player_state_error)
            else -> getString(R.string.player_state_unknown)
        }
        Timber.d("页面$position,播放器状态: $state ($stateDesc)")
    }
}
