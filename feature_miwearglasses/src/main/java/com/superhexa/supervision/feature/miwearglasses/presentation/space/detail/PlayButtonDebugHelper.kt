package com.superhexa.supervision.feature.miwearglasses.presentation.space.detail

import android.view.View
import androidx.constraintlayout.motion.widget.MotionLayout
import timber.log.Timber

/**
 * 播放按钮调试辅助类
 * 用于诊断和修复播放按钮不可见的问题
 */
object PlayButtonDebugHelper {
    
    /**
     * 检查播放按钮的状态并提供修复建议
     */
    fun checkPlayButtonState(
        playButton: View,
        motionLayout: MotionLayout,
        isLandscape: Boolean,
        context: String = ""
    ): PlayButtonDiagnosis {
        val diagnosis = PlayButtonDiagnosis()
        
        // 检查基本属性
        diagnosis.visibility = playButton.visibility
        diagnosis.alpha = playButton.alpha
        diagnosis.isClickable = playButton.isClickable
        diagnosis.width = playButton.width
        diagnosis.height = playButton.height
        diagnosis.x = playButton.x
        diagnosis.y = playButton.y
        
        // 检查MotionLayout状态
        diagnosis.motionLayoutState = motionLayout.currentState
        diagnosis.motionLayoutProgress = motionLayout.progress
        diagnosis.isLandscape = isLandscape
        
        // 分析问题
        diagnosis.issues = analyzeIssues(diagnosis)
        diagnosis.suggestions = generateSuggestions(diagnosis)
        
        // 输出诊断结果
        logDiagnosis(diagnosis, context)
        
        return diagnosis
    }
    
    private fun analyzeIssues(diagnosis: PlayButtonDiagnosis): List<String> {
        val issues = mutableListOf<String>()
        
        if (diagnosis.visibility != View.VISIBLE) {
            issues.add("播放按钮visibility不是VISIBLE: ${getVisibilityString(diagnosis.visibility)}")
        }
        
        if (diagnosis.alpha <= 0.01f) {
            issues.add("播放按钮alpha过低: ${diagnosis.alpha}")
        }
        
        if (diagnosis.width <= 0 || diagnosis.height <= 0) {
            issues.add("播放按钮尺寸异常: width=${diagnosis.width}, height=${diagnosis.height}")
        }
        
        if (diagnosis.isLandscape && diagnosis.alpha > 0.5f) {
            issues.add("横屏模式下播放按钮alpha过高，可能影响UI设计")
        }
        
        if (!diagnosis.isLandscape && diagnosis.alpha < 0.9f) {
            issues.add("竖屏模式下播放按钮alpha过低，用户可能看不清")
        }
        
        return issues
    }
    
    private fun generateSuggestions(diagnosis: PlayButtonDiagnosis): List<String> {
        val suggestions = mutableListOf<String>()
        
        if (diagnosis.visibility != View.VISIBLE) {
            suggestions.add("设置 playButton.visibility = View.VISIBLE")
        }
        
        if (diagnosis.alpha <= 0.01f) {
            if (diagnosis.isLandscape) {
                suggestions.add("横屏模式设置 playButton.alpha = 0.3f")
            } else {
                suggestions.add("竖屏模式设置 playButton.alpha = 1.0f")
            }
        }
        
        if (diagnosis.width <= 0 || diagnosis.height <= 0) {
            suggestions.add("检查MotionLayout约束配置，确保播放按钮有正确的尺寸约束")
            suggestions.add("调用 motionLayout.requestLayout() 强制重新布局")
        }
        
        suggestions.add("使用 motionLayout.post { } 延迟执行状态设置")
        suggestions.add("检查是否有其他代码覆盖了播放按钮的状态")
        
        return suggestions
    }
    
    private fun logDiagnosis(diagnosis: PlayButtonDiagnosis, context: String) {
        Timber.d("=== 播放按钮诊断报告 [$context] ===")
        Timber.d("基本属性:")
        Timber.d("  visibility: ${getVisibilityString(diagnosis.visibility)}")
        Timber.d("  alpha: ${diagnosis.alpha}")
        Timber.d("  isClickable: ${diagnosis.isClickable}")
        Timber.d("  尺寸: ${diagnosis.width} x ${diagnosis.height}")
        Timber.d("  位置: (${diagnosis.x}, ${diagnosis.y})")
        Timber.d("MotionLayout状态:")
        Timber.d("  currentState: ${diagnosis.motionLayoutState}")
        Timber.d("  progress: ${diagnosis.motionLayoutProgress}")
        Timber.d("  屏幕方向: ${if (diagnosis.isLandscape) "横屏" else "竖屏"}")
        
        if (diagnosis.issues.isNotEmpty()) {
            Timber.w("发现问题:")
            diagnosis.issues.forEach { issue ->
                Timber.w("  - $issue")
            }
        }
        
        if (diagnosis.suggestions.isNotEmpty()) {
            Timber.i("修复建议:")
            diagnosis.suggestions.forEach { suggestion ->
                Timber.i("  - $suggestion")
            }
        }
        
        Timber.d("=== 诊断报告结束 ===")
    }
    
    private fun getVisibilityString(visibility: Int): String {
        return when (visibility) {
            View.VISIBLE -> "VISIBLE"
            View.INVISIBLE -> "INVISIBLE"
            View.GONE -> "GONE"
            else -> "UNKNOWN($visibility)"
        }
    }
    
    /**
     * 应用修复建议
     */
    fun applyFixes(
        playButton: View,
        motionLayout: MotionLayout,
        isLandscape: Boolean
    ) {
        Timber.d("应用播放按钮修复...")
        
        // 基本修复
        playButton.visibility = View.VISIBLE
        
        if (isLandscape) {
            playButton.alpha = 0.3f
            playButton.isClickable = false
        } else {
            playButton.alpha = 1.0f
            playButton.isClickable = true
        }
        
        // 强制重新布局
        motionLayout.requestLayout()
        
        // 延迟确认修复效果
        motionLayout.postDelayed({
            val diagnosis = checkPlayButtonState(playButton, motionLayout, isLandscape, "修复后检查")
            if (diagnosis.issues.isEmpty()) {
                Timber.i("播放按钮修复成功！")
            } else {
                Timber.w("播放按钮修复后仍有问题，需要进一步调试")
            }
        }, 100)
    }
}

/**
 * 播放按钮诊断结果
 */
data class PlayButtonDiagnosis(
    var visibility: Int = View.GONE,
    var alpha: Float = 0f,
    var isClickable: Boolean = false,
    var width: Int = 0,
    var height: Int = 0,
    var x: Float = 0f,
    var y: Float = 0f,
    var motionLayoutState: Int = 0,
    var motionLayoutProgress: Float = 0f,
    var isLandscape: Boolean = false,
    var issues: List<String> = emptyList(),
    var suggestions: List<String> = emptyList()
)
