<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <Transition
        app:constraintSetEnd="@+id/vp_video_landscope_fold"
        app:constraintSetStart="@+id/vp_video_portrait"
        app:duration="150"
        app:motionInterpolator="onInterceptTouchReturnSwipe"/>

    <Transition
        app:constraintSetEnd="@+id/vp_video_landscope_unfold"
        app:constraintSetStart="@+id/vp_video_landscope_fold"
        app:duration="200"
        app:motionInterpolator="onInterceptTouchReturnSwipe"/>


    <!-- 定义从可见到隐藏的过渡 -->
    <Transition
        app:constraintSetStart="@id/start"
        app:constraintSetEnd="@id/end"
        app:duration="500">
    </Transition>
    <!-- 初始状态：View 可见 -->
    <ConstraintSet android:id="@+id/start">
        <Constraint
            android:id="@+id/ivPlayToggle222"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:visibilityMode="normal" /> <!-- View 可见 -->
    </ConstraintSet>

    <!-- 结束状态：View 隐藏 -->
    <ConstraintSet android:id="@+id/end">
        <Constraint
            android:id="@+id/ivPlayToggle222"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:visibilityMode="ignore" /> <!-- View 隐藏 -->
    </ConstraintSet>


    <ConstraintSet android:id="@+id/vp_video_portrait">
        <Constraint
            android:id="@+id/baseVideoViewWrapper"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_58"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Constraint
            android:id="@+id/llBottom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <CustomAttribute
                app:attributeName="background"
                app:customColorDrawableValue="@color/transparent" />
        </Constraint>

        <Constraint
            android:id="@+id/ivPlayToggle222"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:layout_marginBottom="20dp"
            android:alpha="1"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:visibilityMode="normal" />

        <Constraint
            android:id="@+id/ivMuteToggle"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:contentDescription="@null"
            android:src="@drawable/ic_o95_selector_mute"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintEnd_toEndOf="parent" />

        <Constraint
            android:id="@+id/ll_quality_status_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_100"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            app:visibilityMode="ignore" />

        <Constraint
            android:id="@+id/preview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_58"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </ConstraintSet>

    <ConstraintSet android:id="@+id/vp_video_landscope_fold">
        <Constraint
            android:id="@+id/baseVideoViewWrapper"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <Constraint
            android:id="@+id/llBottom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <CustomAttribute
                app:attributeName="background"
                app:customColorDrawableValue="@color/black_60" />
        </Constraint>

        <Constraint
            android:id="@+id/ivPlayToggle222"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:layout_marginBottom="20dp"
            android:alpha="0.3"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:visibilityMode="normal" />

        <Constraint
            android:id="@+id/ivMuteToggle"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:alpha="1"
            android:contentDescription="@null"
            android:src="@drawable/ic_o95_selector_mute"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintEnd_toEndOf="parent" />

        <Constraint
            android:id="@+id/ll_quality_status_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_100"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            app:visibilityMode="ignore" />

        <Constraint
            android:id="@+id/previewOrFailedWrapper"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </ConstraintSet>

    <ConstraintSet android:id="@+id/vp_video_landscope_unfold">
        <Constraint
            android:id="@+id/baseVideoViewWrapper"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <Constraint
            android:id="@+id/llBottom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="parent">
            <CustomAttribute
                app:attributeName="background"
                app:customColorDrawableValue="@color/black_60" />
        </Constraint>

        <Constraint
            android:id="@+id/ivPlayToggle222"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:layout_marginBottom="20dp"
            android:alpha="0.3"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:visibilityMode="normal" />

        <Constraint
            android:id="@+id/ivMuteToggle"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:alpha="0"
            android:contentDescription="@null"
            android:src="@drawable/ic_o95_selector_mute"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintEnd_toEndOf="parent" />

        <Constraint
            android:id="@+id/ll_quality_status_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_100"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            app:visibilityMode="ignore" />

        <Constraint
            android:id="@+id/previewOrFailedWrapper"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </ConstraintSet>

</MotionScene>
